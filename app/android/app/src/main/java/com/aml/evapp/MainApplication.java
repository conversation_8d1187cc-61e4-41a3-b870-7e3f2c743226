package com.aml.evapp;

import android.app.Application;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.media.AudioAttributes;
import android.net.Uri;
import android.os.Build;
import com.facebook.react.PackageList;
import com.facebook.react.ReactApplication;
import com.facebook.react.ReactNativeHost;
import com.facebook.react.ReactPackage;
import com.facebook.react.defaults.DefaultNewArchitectureEntryPoint;
import com.facebook.react.defaults.DefaultReactNativeHost;
import com.facebook.soloader.SoLoader;
import java.util.List;

// Reaniamted
import com.facebook.react.bridge.JSIModulePackage;

// AppsFlyer
import com.appsflyer.reactnative.RNAppsFlyerPackage;

// react-native-inappbrowser-reborn
import com.proyecto26.inappbrowser.RNInAppBrowserPackage;

public class MainApplication extends Application implements ReactApplication {

  private final ReactNativeHost mReactNativeHost =
      new DefaultReactNativeHost(this) {
        @Override
        public boolean getUseDeveloperSupport() {
          return BuildConfig.DEBUG;
        }

        @Override
        protected List<ReactPackage> getPackages() {
          @SuppressWarnings("UnnecessaryLocalVariable")
          List<ReactPackage> packages = new PackageList(this).getPackages();
          // Packages that cannot be autolinked yet can be added manually here, for example:
          // packages.add(new MyReactNativePackage());

          return packages;
        }

        @Override
        protected String getJSMainModuleName() {
          return "index";
        }

        @Override
        protected boolean isNewArchEnabled() {
          return BuildConfig.IS_NEW_ARCHITECTURE_ENABLED;
        }

        @Override
        protected Boolean isHermesEnabled() {
          return BuildConfig.IS_HERMES_ENABLED;
        }
      };

  @Override
  public ReactNativeHost getReactNativeHost() {
    return mReactNativeHost;
  }

  @Override
  public void onCreate() {
    super.onCreate();

    // Create custom notification channel with custom sound
    createNotificationChannel();

    SoLoader.init(this, /* native exopackage */ false);
    if (BuildConfig.IS_NEW_ARCHITECTURE_ENABLED) {
      // If you opted-in for the New Architecture, we load the native entry point for this app.
      DefaultNewArchitectureEntryPoint.load();
    }
    ReactNativeFlipper.initializeFlipper(this, getReactNativeHost().getReactInstanceManager());
  }

  /**
   * Create notification channel with custom sound for Android 8.0+
   * This ensures custom notification sounds work properly with Airship
   *
   * Configuration is centralized in src/config/notificationSound.ts
   * To change the sound file, update SOUND_FILE_NAME in that file
   */
  private void createNotificationChannel() {
      // These values should match src/config/notificationSound.ts
      String channelId = "bp_pulse_notifications";
      String channelName = "BP Pulse Notifications";
      String channelDescription = "Notifications for BP Pulse app with custom sound";
      int importance = NotificationManager.IMPORTANCE_HIGH;

      NotificationChannel channel = new NotificationChannel(channelId, channelName, importance);
      channel.setDescription(channelDescription);

      // Set custom sound - update R.raw.{filename} if sound file changes
      Uri soundUri = Uri.parse("android.resource://" + getPackageName() + "/" + R.raw.notification_custom);
      AudioAttributes audioAttributes = new AudioAttributes.Builder()
          .setContentType(AudioAttributes.CONTENT_TYPE_SONIFICATION)
          .setUsage(AudioAttributes.USAGE_NOTIFICATION)
          .build();
      channel.setSound(soundUri, audioAttributes);

      // Enable vibration and lights
      channel.enableVibration(true);
      //channel.enableLights(true);
      //channel.setLightColor(android.graphics.Color.BLUE);

      // Register the channel with the system
      NotificationManager notificationManager = getSystemService(NotificationManager.class);
      if (notificationManager != null) {
        notificationManager.createNotificationChannel(channel);
        android.util.Log.d("MainApplication", "Created notification channel: " + channelId + " with custom sound");
      }
  }
}